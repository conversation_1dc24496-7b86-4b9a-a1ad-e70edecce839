import 'server-only';

import type { Session } from '@/types';

import { eq } from 'drizzle-orm';
import { unstable_cache as cache } from 'next/cache';
import { getURLFromRedirectError } from 'next/dist/client/components/redirect';
import { isRedirectError } from 'next/dist/client/components/redirect-error';
import { redirect } from 'next/navigation';

import { dbAdmin } from '@/database';
import { usersTable } from '@/database/schema';
import { getSupabaseServerClient } from '@/lib/supabase/config/server';

import { SiteConfig } from '@/configuration';
import {
  CacheKey,
  Caching,
  defaultRevalidateTimeInSeconds,
} from '@/lib/caching';
import { logger } from '@/utils/logger';

type DashboardData = {
  session: Session;
};

export async function loadDashboardData(): Promise<DashboardData | undefined> {
  try {
    const supabase = await getSupabaseServerClient();

    const { data: authUser, error } = await supabase.auth.getUser();

    if (error || !authUser.user) {
      logger.error({ error }, '❌ ERROR IN GETTING AUTH USER');
      return redirect(SiteConfig.paths.auth.signIn);
    }

    const userId = authUser.user.id;

    return cache(
      async () => {
        const [user] = await dbAdmin
          .select()
          .from(usersTable)
          .where(eq(usersTable.id, userId));

        if (!user) {
          throw new Error('❌ ERROR IN GETTING AUTH USER');
        }

        return {
          session: {
            user,
          },
        };
      },
      Caching.createKeyParts(CacheKey.DashboardData, userId),
      {
        revalidate: defaultRevalidateTimeInSeconds,
        tags: [Caching.createTag(CacheKey.DashboardData, userId)],
      }
    )();
  } catch (error) {
    if (isRedirectError(error)) {
      return redirect(getURLFromRedirectError(error));
    }
    logger.error({ error }, '❌ ERROR IN LOADING DASHBOARD DATA');
    return redirect('/');
  }
}
