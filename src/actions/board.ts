'use server';

import { dbAdmin } from '@/database';
import { boardsTable } from '@/database/schema';
import { z } from 'zod';

import { logger } from '@/utils/logger';
import { authActionClient } from '@/utils/safe-action';

const createBoardSchema = z.object({
  boardName: z.string().min(3, 'Board name is required'),
});

export const createBoardAction = authActionClient
  .schema(createBoardSchema)
  .metadata({ name: 'create-board' })
  .action(async ({ ctx: { authUser }, parsedInput: { boardName } }) => {
    const userId = authUser.id;

    const [newBoard] = await dbAdmin
      .insert(boardsTable)
      .values({ name: boardName, userId })
      .returning({ boardId: boardsTable.id });

    const { boardId } = newBoard ?? {};

    if (!boardId) {
      throw new Error('❌ ERROR IN CREATING BOARD');
    }

    logger.info({ userId, boardId }, '✅ BOARD CREATED SUCCESSFULLY');

    return boardId;
  });
