'use server';

import { getSupabaseServerClient } from '@/lib/supabase/config/server';

import {
  forgotPasswordFormSchema,
  resendLinkFormSchema,
  signInWithEmailPasswordFormSchema,
  signInWithOAuthProviderFormSchema,
  signUpEmailPasswordFormSchema,
} from '@/core/schemas/auth';
import { rateLimitedActionClient } from '@/utils/safe-action';

import { SiteConfig } from '@/configuration';
import { logger } from '@/utils/logger';
import { redirect } from 'next/navigation';

export const signInWithEmailPasswordAction = rateLimitedActionClient
  .schema(signInWithEmailPasswordFormSchema)
  .metadata({ name: 'sign-in-with-email-password' })
  .action(async ({ parsedInput: data }) => {
    const { email, password, captchaToken, emailRedirectTo } = data;

    logger.info({ email, emailRedirectTo }, '🚀 SIGNING IN...');

    const supabase = await getSupabaseServerClient();
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
      options: {
        captchaToken,
      },
    });

    if (error) {
      if (error.status === 403) {
        logger.error({ error }, '❌ EMAIL NOT VERIFIED');
        throw new Error('Please verify your email address');
      }
      logger.error({ error }, '❌ SIGN IN ERROR');
      throw new Error(error.message);
    }

    logger.info('✅ USER SUCCESSFULLY SIGNED IN');

    if (!emailRedirectTo) return;

    redirect(emailRedirectTo);
  });

export const signUpWithEmailPasswordAction = rateLimitedActionClient
  .schema(signUpEmailPasswordFormSchema)
  .metadata({ name: 'sign-up-with-email-password' })
  .action(async ({ parsedInput: data }) => {
    const supabase = await getSupabaseServerClient();
    const { email, password, captchaToken, emailRedirectTo } = data;

    logger.info('🚀 SIGNING UP...');

    const { error, data: response } = await supabase.auth.signUp({
      email,
      password,
      options: {
        captchaToken,
        emailRedirectTo,
      },
    });

    if (error || !response.user) {
      logger.error({ error, email }, '❌ ERROR SIGNING UP!');
      throw error;
    }

    const identities = response?.user?.identities ?? [];
    if (identities.length === 0) {
      logger.error({ error, email }, '❌ USER ALREADY REGISTERED!');
      throw error;
    }

    logger.info({ response }, '✅ USER SUCCESSFULLY SIGNED UP');
  });

export const signInWithOAuthProviderAction = rateLimitedActionClient
  .schema(signInWithOAuthProviderFormSchema)
  .metadata({ name: 'sign-in-with-oauth-provider' })
  .action(async ({ parsedInput: { provider, redirectTo } }) => {
    const supabase = await getSupabaseServerClient();

    // biome-ignore format: Custom formatted
    const options = SiteConfig.isProduction ? {} : {
      queryParams: {
        access_type: 'offline',
        prompt: 'consent',
      },
    };

    const { data: response, error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo,
        ...options,
      },
    });

    if (error || !response) {
      logger.error({ error }, '❌ ERROR SIGNING IN WITH OAUTH PROVIDER');
      throw error;
    }

    logger.info({ response }, '✅ SUCCESSFULLY SIGNED IN WITH PROVIDER');
    redirect(response.url);
  });

export const forgotPasswordAction = rateLimitedActionClient
  .schema(forgotPasswordFormSchema)
  .metadata({ name: 'forgot-password' })
  .action(async ({ parsedInput: { email, captchaToken, redirectTo } }) => {
    const supabase = await getSupabaseServerClient();
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      captchaToken,
      redirectTo,
    });

    if (error) {
      logger.error({ error }, '❌ ERROR SENDING RESET LINK');
      throw new Error(error.message);
    }

    logger.info('✅ RESET LINK SENT SUCCESSFULLY');
  });

export const resendLinkAction = rateLimitedActionClient
  .schema(resendLinkFormSchema)
  .metadata({ name: 'resend-link' })
  .action(async ({ parsedInput: { email } }) => {
    const supabase = await getSupabaseServerClient();

    const { error } = await supabase.auth.resend({
      email,
      type: 'signup',
    });

    if (error) {
      logger.error({ error }, '❌ ERROR RESENDING PASSWORD RESET LINK');
      throw error;
    }

    logger.info('✅ RESENT PASSWORD RESET LINK SUCCESSFULLY');
  });
