'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { EyeIcon, EyeOffIcon, LockIcon, MailIcon } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { Captcha } from '@/components/security/captcha';
import { ShowPasswordToggle } from '@/components/show-password-toggle';
import { Button, buttonVariants } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import { FormInput } from '@/components/ui/form/form-input';
import { SeparatorWithText } from '@/components/ui/separator-with-text';

import { signUpWithEmailPasswordAction } from '@/actions/auth';
import {
  type SignUpEmailPasswordFormValues,
  signUpEmailPasswordFormSchema,
} from '@/core/schemas/auth';

import { SiteConfig } from '@/configuration';
import { getRedirectURL } from '@/utils/auth';
import { cn } from '@/utils/cn';
import { OAuthForm } from '../_components/oauth-form';

export function SignUpForm() {
  const searchParams = useSearchParams();
  const nextUrl = searchParams.get('next') ?? '';

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const defaultValues = getDefaultValues();
  const form = useForm<SignUpEmailPasswordFormValues>({
    resolver: zodResolver(signUpEmailPasswordFormSchema),
    defaultValues,
  });

  const onCaptchaVerify = (token: string) => {
    form.setValue('captchaToken', token);
  };

  async function onSubmit(data: SignUpEmailPasswordFormValues) {
    setIsLoading(true);
    const emailRedirectTo = getRedirectURL(window.location.origin, nextUrl);
    const promise = signUpWithEmailPasswordAction({
      ...data,
      emailRedirectTo,
    });
    toast.promise(promise, {
      loading: 'Creating account...',
      success: () => {
        form.reset(defaultValues);
        return 'Confirmation email sent! Check your email';
      },
      error: 'Failed to create account',
      finally: () => setIsLoading(false),
    });
  }

  return (
    <Card className="w-full max-w-[400px] bg-background">
      <CardHeader className="text-center">
        <h1 className="text-2xl font-semibold">Create an account</h1>
        <p className="text-sm text-muted-foreground">
          Enter your details to get started
        </p>
      </CardHeader>
      <CardContent className="space-y-3">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormInput
              form={form}
              type="email"
              fieldName="email"
              fieldLabel="Email"
              autoComplete="email"
              autoCapitalize="none"
              autoCorrect="off"
              placeholder="<EMAIL>"
              leadingIcon={<MailIcon className="size-4" />}
              required
            />

            <FormInput
              form={form}
              type={showPassword ? 'text' : 'password'}
              fieldName="password"
              fieldLabel="Password"
              autoComplete="new-password"
              autoCapitalize="none"
              autoCorrect="off"
              placeholder="••••••••"
              leadingIcon={<LockIcon className="size-4" />}
              trailingIcon={
                <ShowPasswordToggle
                  showPassword={showPassword}
                  onTogglePassword={() => setShowPassword(!showPassword)}
                />
              }
              required
            />

            <FormInput
              form={form}
              type={showConfirmPassword ? 'text' : 'password'}
              fieldName="confirmPassword"
              fieldLabel="Confirm Password"
              autoComplete="new-password"
              autoCapitalize="none"
              autoCorrect="off"
              placeholder="••••••••"
              leadingIcon={<LockIcon className="size-4" />}
              trailingIcon={
                <ShowPasswordToggle
                  showPassword={showConfirmPassword}
                  onTogglePassword={() =>
                    setShowConfirmPassword(!showConfirmPassword)
                  }
                />
              }
              required
            />

            <Captcha
              form={form}
              fieldName="captchaToken"
              onVerify={onCaptchaVerify}
            />

            <Button type="submit" className="w-full" disabled={isLoading}>
              Sign up
            </Button>
          </form>
        </Form>

        <SeparatorWithText>Or continue with</SeparatorWithText>

        <OAuthForm loading={isLoading} onLoadingChange={setIsLoading} />

        <p className="text-center text-sm text-muted-foreground">
          <span>Already have an account?</span>{' '}
          <Link
            href={SiteConfig.paths.auth.signIn}
            className={cn(buttonVariants({ variant: 'link' }), 'px-0 h-5')}
          >
            Sign in
          </Link>
        </p>
      </CardContent>
    </Card>
  );
}

function getDefaultValues(): SignUpEmailPasswordFormValues {
  return {
    email: '',
    password: '',
    confirmPassword: '',
    captchaToken: '',
    emailRedirectTo: '',
  };
}
