'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { Captcha } from '@/components/security/captcha';
import { Button, buttonVariants } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import { FormInput } from '@/components/ui/form/form-input';
import {
  type ResendLinkFormValues,
  resendLinkFormSchema,
} from '@/core/schemas/auth';

import { resendLinkAction } from '@/actions/auth';
import { cn } from '@/utils/cn';
import { MailIcon } from 'lucide-react';

export const ResendLinkForm: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<ResendLinkFormValues>({
    resolver: zodResolver(resendLinkFormSchema),
    defaultValues: {
      email: '',
      captchaToken: '',
    },
  });

  const onCaptchaVerify = (token: string) => {
    form.setValue('captchaToken', token);
  };

  const onSubmit = useCallback(async (data: ResendLinkFormValues) => {
    setIsLoading(true);
    const promise = resendLinkAction(data);
    toast.promise(promise, {
      loading: 'Sending link...',
      success: 'Link sent!',
      error: (error) => `Failed to send link. ${error}`,
      finally: () => setIsLoading(false),
    });
  }, []);

  return (
    <Card className="border-none shadow-none">
      <CardHeader className="px-0">
        <CardTitle>Oops! We could not authenticate you.</CardTitle>
        <CardDescription>
          We had an issue with verifying your account. Enter your email to
          receive an authentication link again.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 px-0">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
            <FormInput
              form={form}
              type="email"
              fieldName="email"
              fieldLabel="Email"
              autoComplete="email"
              autoCapitalize="none"
              placeholder="<EMAIL>"
              leadingIcon={<MailIcon className="size-4" />}
              autoCorrect="off"
              required
            />

            <Captcha
              form={form}
              fieldName="captchaToken"
              onVerify={onCaptchaVerify}
            />

            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
              loading={isLoading}
            >
              Resend Link
            </Button>
            <Link
              className={cn(buttonVariants({ variant: 'ghost' }), 'w-full')}
              href={'/auth/sign-in'}
            >
              Sign In
            </Link>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
