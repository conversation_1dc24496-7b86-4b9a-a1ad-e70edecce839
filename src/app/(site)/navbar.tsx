'use client';

import type { UsersResponse } from '@/database/types';

import {
  BadgeCheckIcon,
  ChevronRightIcon,
  CommandIcon,
  CreditCardIcon,
  LayoutDashboardIcon,
  LogOutIcon,
} from 'lucide-react';
import Link from 'next/link';

import { ToggleTheme } from '@/components/toggle-theme';
import { Avatar, AvatarImage } from '@/components/ui/avatar';
import { Button, buttonVariants } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { If } from '@/components/ui/if';
import { SiteConfig } from '@/configuration';
import { useSession } from '@/hooks/context';
import { useSupabase } from '@/lib/supabase/hooks/use-supabase';
import { cn } from '@/utils/cn';

export function Navbar() {
  const { session } = useSession();

  const { user } = session ?? {};

  return (
    <nav className="relative">
      <div className="absolute inset-x-4 top-4 flex items-center justify-between px-4 py-2 border border-border rounded-xl bg-background/20 backdrop-blur-md">
        <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
          <CommandIcon className="size-4" />
        </div>
        <div className="flex items-center gap-2">
          <ToggleTheme />

          <If condition={!user?.id}>
            <Link
              href={SiteConfig.paths.auth.signIn}
              className={cn(buttonVariants({ size: 'sm', variant: 'outline' }))}
            >
              Sign In
            </Link>
            <Link
              href={SiteConfig.paths.auth.signUp}
              className={cn(buttonVariants({ size: 'sm' }))}
            >
              Get Started
              <ChevronRightIcon />
            </Link>
          </If>

          <If condition={!!user?.id}>
            <UserDropdown user={user} />
          </If>
        </div>
      </div>
    </nav>
  );
}

function UserDropdown({ user }: { user?: UsersResponse }) {
  const supabase = useSupabase();

  const userName = user?.name ?? 'Anonymous';
  const userEmail = user?.email ?? '';
  const userAvatar = user?.avatar ?? '/images/placeholder-avatar.jpg';

  const signOut = async () => {
    await supabase.auth.signOut();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button size="icon" variant="ghost">
          <Avatar className="size-8 rounded-lg">
            <AvatarImage src={userAvatar} alt={userName} />
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        sideOffset={4}
        className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
      >
        <DropdownMenuLabel className="p-0 font-normal">
          <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
            <Avatar className="size-8 rounded-lg">
              <AvatarImage src={userAvatar} alt={userName} />
            </Avatar>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-semibold">{userName}</span>
              <span className="truncate text-xs text-muted-foreground">
                {userEmail}
              </span>
            </div>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        <DropdownMenuItem asChild>
          <Link href={SiteConfig.paths.dashboard.root}>
            <LayoutDashboardIcon />
            Dashboard
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <Link href={SiteConfig.paths.settings.account}>
            <BadgeCheckIcon />
            Account
          </Link>
        </DropdownMenuItem>

        {/* <DropdownMenuItem>
          <CreditCardIcon />
          Billing
        </DropdownMenuItem> */}

        <DropdownMenuSeparator />

        <DropdownMenuItem onClick={signOut}>
          <LogOutIcon />
          Log out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
