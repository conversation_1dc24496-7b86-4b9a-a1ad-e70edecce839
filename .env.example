# SITE CONFIGURATION
NODE_ENV=local # For Local Development
NEXT_PUBLIC_ENVIRONMENT=local # For Local Development
NEXT_PUBLIC_SITE_URL=http://localhost:3000 # For Local Development

# SUPABASE
ADMIN_DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres

NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# GOOGLE
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# RESEND
RESEND_API_KEY=
EMAIL_SENDER="Muhammad Tehseen Khan <<EMAIL>>"

# CAPTCHA
NEXT_PUBLIC_CAPTCHA_SITE_KEY=1x00000000000000000000AA
CAPTCHA_SECRET_KEY=1x0000000000000000000000000000000AA

# UPSTASH REDIS
UPSTASH_REDIS_REST_URL=
UPSTASH_REDIS_REST_TOKEN=